#!/usr/bin/env python3
"""
基于规则文件的工作票校验主程序
使用方法：python validate_with_rules.py <HTML文件路径>
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from scripts.rule_based_validator import RuleBasedValidator
from scripts.ticket_parser import TicketParser

def print_banner():
    """打印程序标题"""
    print("=" * 60)
    print("工作票自动校验系统 (基于规则文件)")
    print("=" * 60)

def print_usage():
    """打印使用说明"""
    print("使用方法:")
    print("  python validate_with_rules.py <HTML文件路径>")
    print("")
    print("示例:")
    print("  python validate_with_rules.py data/html/txgzp/txgzp2.html")
    print("")
    print("配置文件:")
    print("  规则文件: config/validation_rules_simple.json")
    print("  配置文件: config/config.yaml")

def validate_html_file(html_file):
    """校验HTML文件"""
    print(f"正在校验文件: {html_file}")
    print("-" * 40)
    
    # 检查文件是否存在
    if not os.path.exists(html_file):
        print(f"错误: 文件 '{html_file}' 不存在")
        return False
    
    try:
        # 读取HTML文件
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 解析HTML
        print("1. 解析工作票数据...")
        parser = TicketParser()
        ticket_data, parsing_errors = parser.parse(html_content)
        
        if parsing_errors:
            print("   解析时发现以下问题:")
            for error in parsing_errors:
                print(f"   - {error}")
        
        ticket_id = ticket_data.get('编号', '未知编号')
        print(f"   工作票编号: {ticket_id}")
        
        # 执行规则校验
        print("\n2. 执行规则校验...")
        validator = RuleBasedValidator(ticket_data)
        results = validator.validate()
        
        # 显示结果
        print("\n3. 校验结果")
        print("=" * 40)
        
        stats = results.get('stats', {})
        print(f"统计信息:")
        print(f"  不合格项: {stats.get('不合格', 0)}")
        print(f"  不规范项: {stats.get('不规范', 0)}")
        print(f"  需人工确认: {stats.get('请人工确认', 0)}")
        
        rule_stats = results.get('rule_execution_stats', {})
        print(f"\n规则执行统计:")
        print(f"  总规则数: {rule_stats.get('total', 0)}")
        print(f"  通过: {rule_stats.get('passed', 0)}")
        print(f"  不通过: {rule_stats.get('failed', 0)}")
        print(f"  跳过: {rule_stats.get('skipped', 0)}")
        print(f"  错误: {rule_stats.get('error', 0)}")
        
        # 显示详细结果
        print(f"\n{results.get('summary_brief', '')}")
        
        if stats.get('不合格', 0) > 0 or stats.get('不规范', 0) > 0:
            print(f"\n详细问题列表:")
            print(f"{results.get('summary_detailed', '')}")
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"validation_result_{ticket_id}_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细结果已保存到: {result_file}")
        
        return True
        
    except Exception as e:
        print(f"校验过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print_usage()
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    # 执行校验
    success = validate_html_file(html_file)
    
    if success:
        print("\n校验完成!")
        sys.exit(0)
    else:
        print("\n校验失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()
