import json

GENERATE_PROMPT = """
以下是工作票的解析数据和需要人工确认的条目。请作为电力调度工作票审批专员，对工作票中每个'请人工确认'项进行分析，给出你的判断以及理由。

票面数据摘要：
{ticket_data}

需要确认的项：
{manual_items}

输出格式：
对于每个规则，输出以下结构化JSON（仅输出 JSON 数组，不要输出额外的文字说明）：
[
  {{
    "规则": "<规则编号><规则编号对应的规则内容>",
    "判断": "<合规/不合规/不规范/请人工确认>",
    "依据"："<票面数据摘要中与词条规则对应的数据>",
    "理由": "<详细理由说明（如果判断为'请人工确认'，请在结尾以 `需要补充:` 开头列出需线下核实或补充的信息或检查步骤）>"
  }}
]

要求与说明：
- 请确保输出为有效的 JSON 数组，且每个数组元素尽量压缩为一行（不要在数组元素内部产生多段换行），方便机器解析。
- 若规则编号以 D1_ 或 D1- 开头，属于“不合规”类型判据。
- 若规则编号以 D2_ 或 D2- 开头，属于“不规范”类型判据。
- **如果根据票面数据和附录D可以判断的情况，请务必作出判断，尽量少作出“请人工确认”的判断。**
- 对于需要外部数据或现场检查才能确定的情况，请判断为“请人工确认”，并在 `需要补充:` 后列出具体需要的外部数据或现场检查步骤（例如：查看签名原件、调用计划停电时间接口、现场核验标示牌是否悬挂等）。
- 不要输出除上面 JSON 之外的任何多余文本（例如“解释：”“备注：”等）。

示例（仅为格式示例，实际返回必须是有效 JSON）：
[
  {{"规则": "D1-2计划工作时间超出计划停电时间或已过期。", 
  "判断": "不合规",
  "依据"： "计划结束时间 (2022-05-18 17:00)",
  "理由": "计划结束时间 (2022-05-18 17:00) 早于当前时间 (2025-08-22 10:41)。“}}
]
"""