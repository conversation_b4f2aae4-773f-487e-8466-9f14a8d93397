import os
from typing import Optional
import yaml
import logging

class Config:
    _instance = None
    _config_data = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._load_config()
        return cls._instance

    def _load_config(self):
        config_path = os.getenv('CONFIG_PATH', './config/config.yaml')
        try:
            with open(config_path, 'r') as f:
                self._config_data = yaml.safe_load(f)
        except FileNotFoundError:
            self._config_data = {}
            logging.warning(f"Config file '{config_path}' not found. Using default empty config.")
        except yaml.YAMLError as e:
            self._config_data = {}
            logging.error(f"Error parsing config file: {e}. Using default empty config.")

    def get(self, key: str, default: Optional[any] = None):
        return self._config_data.get(key, default)