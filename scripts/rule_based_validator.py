from datetime import datetime
import re
import json
import os
from collections import defaultdict
from typing import Dict, List, Any, Optional
try:
    from .config import Config
except ImportError:
    from scripts.config import Config

class RuleBasedValidator:
    """
    基于规则文件的工作票校验器。
    读取JSON格式的规则文件，按规则逐条执行校验。
    """
    
    def __init__(self, ticket_data: Dict, rules_file: Optional[str] = None):
        """
        初始化校验器。
        :param ticket_data: 从 ticket_parser.py 解析出的字典数据
        :param rules_file: 规则文件路径，如果为None则从配置中读取
        """
        self.ticket = ticket_data if isinstance(ticket_data, dict) else {}
        self.results = defaultdict(list)
        self.ticket_id = self.get_nested_value("编号", "未知编号")
        
        # 加载规则文件
        if rules_file is None:
            config = Config()
            rules_file = config.get('validation_rules_file', 'config/validation_rules.json')
        
        self.rules = self._load_rules(rules_file)
        
        # 规则执行统计
        self.rule_stats = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'error': 0
        }

    def _load_rules(self, rules_file: str) -> List[Dict]:
        """加载规则文件"""
        try:
            if not os.path.exists(rules_file):
                print(f"警告: 规则文件 {rules_file} 不存在，使用空规则列表")
                return []
                
            with open(rules_file, 'r', encoding='utf-8') as f:
                rules = json.load(f)
                
            if not isinstance(rules, list):
                raise ValueError("规则文件必须包含规则对象的数组")
                
            print(f"成功加载 {len(rules)} 条规则")
            return rules
            
        except Exception as e:
            print(f"加载规则文件时出错: {e}")
            return []

    def get_nested_value(self, field_path: str, default=None):
        """
        安全地从嵌套字典中获取值。
        例如: "工作许可.许可时间"
        """
        keys = field_path.split('.')
        value = self.ticket
        for key in keys:
            if isinstance(value, dict):
                value = value.get(key)
            elif isinstance(value, list) and key.isdigit() and int(key) < len(value):
                value = value[int(key)]
            else:
                return default
            if value is None:
                return default
        return value

    def _add_result(self, category: str, rule_id: str, description: str, evidence: str = "", suggestion: str = ""):
        """添加一条结构化的检查结果"""
        result_entry = {
            "规则编号": rule_id,
            "问题描述": description,
            "主要依据": evidence,
            "建议操作": suggestion
        }
        # 防止重复添加完全相同的结果
        if result_entry not in self.results[category]:
            self.results[category].append(result_entry)

    def validate(self) -> Dict:
        """
        执行所有规则检查并返回结果。
        """
        if not self.ticket:
            self._add_result("不合格", "FATAL", "输入的工作票数据为空或格式错误。", 
                             "传入的 ticket_data 为空。", "请检查上游解析流程是否正确。")
            return self.get_results()

        print(f"\n开始执行规则校验，共 {len(self.rules)} 条规则...")
        
        for i, rule in enumerate(self.rules, 1):
            try:
                self.rule_stats['total'] += 1
                rule_id = rule.get('rule_id', f'RULE_{i}')
                
                print(f"[{i}/{len(self.rules)}] 执行规则 {rule_id}: {rule.get('description', '无描述')}")
                
                # 执行单条规则
                result = self._execute_rule(rule)
                
                if result == 'passed':
                    self.rule_stats['passed'] += 1
                    print(f"  ✓ 通过")
                elif result == 'failed':
                    self.rule_stats['failed'] += 1
                    print(f"  ✗ 不通过")
                elif result == 'skipped':
                    self.rule_stats['skipped'] += 1
                    print(f"  - 跳过")
                    
            except Exception as e:
                self.rule_stats['error'] += 1
                print(f"  ⚠ 规则执行出错: {e}")
                self._add_result("系统错误", rule_id, f"规则执行出错: {str(e)}", 
                                 f"规则定义: {rule}", "请检查规则配置")

        self._print_rule_stats()
        return self.get_results()

    def _execute_rule(self, rule: Dict) -> str:
        """
        执行单条规则。
        返回: 'passed', 'failed', 'skipped'
        """
        rule_id = rule.get('rule_id', 'UNKNOWN')
        category = rule.get('category', '不合格')
        description = rule.get('description', '未知问题')
        field_path = rule.get('field_path', '')
        condition = rule.get('condition', '')
        evidence_template = rule.get('evidence_template', '检查失败')
        suggestion = rule.get('suggestion', '请检查相关内容')

        # 获取字段值
        field_value = self.get_nested_value(field_path)
        
        # 执行条件检查
        check_result = self._check_condition(rule, field_value)
        
        if check_result:
            # 生成证据文本
            evidence = evidence_template.format(
                value=field_value or '空',
                field_path=field_path,
                rule_id=rule_id
            )
            
            self._add_result(category, rule_id, description, evidence, suggestion)
            return 'failed'
        
        return 'passed'

    def _check_condition(self, rule: Dict, field_value: Any) -> bool:
        """
        检查规则条件是否满足（返回True表示规则违反，需要报告问题）
        """
        condition = rule.get('condition', '')
        
        if condition == 'not_empty':
            return self._check_not_empty(rule, field_value)
        elif condition == 'matches_regex':
            return self._check_regex(rule, field_value)
        elif condition == 'contains_keywords':
            return self._check_keywords(rule, field_value)
        elif condition == 'datetime_check':
            return self._check_datetime(rule, field_value)
        elif condition == 'datetime_compare':
            return self._check_datetime_compare(rule, field_value)
        elif condition == 'numeric_compare':
            return self._check_numeric_compare(rule, field_value)
        elif condition == 'conditional_check':
            return self._check_conditional(rule, field_value)
        elif condition == 'not_empty_or_invalid':
            return self._check_not_empty_or_invalid(rule, field_value)
        elif condition == 'dual_signature_check':
            return self._check_dual_signature(rule, field_value)
        elif condition == 'change_procedure_check':
            return self._check_change_procedure(rule, field_value)
        elif condition == 'interruption_check':
            return self._check_interruption(rule, field_value)
        elif condition == 'team_signature_check':
            return self._check_team_signature(rule, field_value)
        else:
            print(f"  警告: 未知的条件类型 '{condition}'")
            return False

    def _check_not_empty(self, rule: Dict, field_value: Any) -> bool:
        """检查字段是否为空"""
        is_empty = not field_value or (isinstance(field_value, str) and field_value.strip() == '')
        return is_empty

    def _check_regex(self, rule: Dict, field_value: Any) -> bool:
        """检查正则表达式匹配"""
        if not field_value:
            return False  # 空值不进行正则检查
            
        regex_pattern = rule.get('regex', '')
        negate = rule.get('negate', False)
        
        try:
            match = re.search(regex_pattern, str(field_value))
            result = bool(match)
            return not result if negate else not result
        except re.error as e:
            print(f"  正则表达式错误: {e}")
            return False

    def _check_keywords(self, rule: Dict, field_value: Any) -> bool:
        """检查关键字包含"""
        if not field_value:
            return rule.get('negate', False)  # 空值的处理取决于negate设置
            
        keywords = rule.get('keywords', [])
        match_mode = rule.get('match_mode', 'any')  # 'any' 或 'all'
        negate = rule.get('negate', False)
        
        field_str = str(field_value).lower()
        
        if match_mode == 'any':
            found = any(keyword.lower() in field_str for keyword in keywords)
        else:  # 'all'
            found = all(keyword.lower() in field_str for keyword in keywords)
            
        return not found if negate else not found

    def _check_datetime(self, rule: Dict, field_value: Any) -> bool:
        """检查日期时间"""
        if not field_value:
            return True  # 空值视为检查失败
            
        datetime_format = rule.get('datetime_format', '%Y-%m-%d %H:%M')
        check_type = rule.get('check_type', 'not_past')
        
        try:
            dt = datetime.strptime(str(field_value), datetime_format)
            now = datetime.now()
            
            if check_type == 'not_past':
                return dt < now  # 过期返回True（检查失败）
            elif check_type == 'not_future':
                return dt > now  # 未来时间返回True（检查失败）
                
        except ValueError:
            return True  # 格式错误视为检查失败
            
        return False

    def _check_datetime_compare(self, rule: Dict, field_value: Any) -> bool:
        """检查日期时间比较"""
        if not field_value:
            return True
            
        compare_field = rule.get('compare_with_field', '')
        compare_value = self.get_nested_value(compare_field)
        
        if not compare_value:
            return True
            
        datetime_format = rule.get('datetime_format', '%Y-%m-%d %H:%M')
        compare_type = rule.get('compare_type', 'less_than')
        
        try:
            dt1 = datetime.strptime(str(field_value), datetime_format)
            dt2 = datetime.strptime(str(compare_value), datetime_format)
            
            if compare_type == 'less_than':
                return dt1 >= dt2  # 不小于返回True（检查失败）
            elif compare_type == 'greater_than':
                return dt1 <= dt2  # 不大于返回True（检查失败）
                
        except ValueError:
            return True
            
        return False

    def _check_numeric_compare(self, rule: Dict, field_value: Any) -> bool:
        """检查数值比较"""
        compare_field = rule.get('compare_with_field', '')
        compare_type = rule.get('compare_type', 'equals')
        
        if compare_type == 'equals_count_plus_one':
            # 特殊处理：检查总人数是否等于班组人数+1
            team_members = self.get_nested_value(compare_field, [])
            if not isinstance(team_members, list):
                return True
                
            actual_count = len(team_members) + 1  # +1 是工作负责人
            
            # 从申报总数中提取数字
            claimed_count = 0
            if field_value:
                match = re.search(r'\d+', str(field_value))
                if match:
                    claimed_count = int(match.group())
                    
            return claimed_count != actual_count
            
        return False

    def _check_conditional(self, rule: Dict, field_value: Any) -> bool:
        """检查条件性规则"""
        if_condition = rule.get('if_condition', {})
        then_check = rule.get('then_check', {})
        
        # 检查if条件
        if_field = if_condition.get('field_path', rule.get('field_path', ''))
        if_value = self.get_nested_value(if_field)
        
        # 构造临时规则对象来检查if条件
        temp_rule = dict(if_condition)
        if_result = self._check_condition(temp_rule, if_value)
        
        # 如果if条件不满足，跳过检查
        if if_result:
            return False
            
        # 检查then条件
        then_field = then_check.get('field_path', rule.get('field_path', ''))
        then_value = self.get_nested_value(then_field) if then_field != rule.get('field_path', '') else field_value
        
        temp_rule = dict(then_check)
        return self._check_condition(temp_rule, then_value)

    def _check_not_empty_or_invalid(self, rule: Dict, field_value: Any) -> bool:
        """检查字段是否为空或包含无效值"""
        if not field_value:
            return True
            
        invalid_values = rule.get('invalid_values', [])
        field_str = str(field_value).strip()
        
        return field_str in invalid_values

    def _check_dual_signature(self, rule: Dict, field_value: Any) -> bool:
        """检查双签发"""
        also_check_field = rule.get('also_check_field', '')
        other_value = self.get_nested_value(also_check_field)
        
        return not field_value or not other_value

    def _check_change_procedure(self, rule: Dict, field_value: Any) -> bool:
        """检查变更手续"""
        if not field_value or not isinstance(field_value, dict):
            return False  # 没有变更记录，不检查
            
        # 如果有变更记录，检查必要字段
        if any(field_value.values()):
            required_fields = rule.get('required_fields', [])
            missing = [field for field in required_fields if not field_value.get(field)]
            return len(missing) > 0
            
        return False

    def _check_interruption(self, rule: Dict, field_value: Any) -> bool:
        """检查工作间断记录"""
        if not field_value or not isinstance(field_value, list):
            return False  # 没有间断记录，不检查
            
        required_fields = rule.get('required_fields', [])
        
        for record in field_value:
            if isinstance(record, dict):
                missing = [field for field in required_fields if not record.get(field)]
                if missing:
                    return True  # 有缺失字段
                    
        return False

    def _check_team_signature(self, rule: Dict, field_value: Any) -> bool:
        """检查班组签名"""
        compare_field = rule.get('compare_with_field', '')
        team_members = self.get_nested_value(compare_field, [])
        
        if not team_members:
            return False  # 没有班组成员，不检查
            
        if not field_value:
            return True  # 有班组成员但没有签名
            
        # 比较签名数量和班组人数
        if isinstance(field_value, list):
            return len(field_value) != len(team_members)
            
        return True

    def _print_rule_stats(self):
        """打印规则执行统计"""
        stats = self.rule_stats
        print(f"\n规则执行统计:")
        print(f"  总计: {stats['total']}")
        print(f"  通过: {stats['passed']}")
        print(f"  不通过: {stats['failed']}")
        print(f"  跳过: {stats['skipped']}")
        print(f"  错误: {stats['error']}")

    @staticmethod
    def _sort_key(result_item):
        """为结果条目生成排序键，确保按规则编号 D-X-Y(Z) 排序。"""
        rule = result_item.get('规则编号', '')
        match = re.match(r'D(\d+)-(\d+)(?:-(\d+))?(?:\((\d+)\))?', rule)
        if match:
            d_part = int(match.group(1))
            x_part = int(match.group(2))
            y_part = int(match.group(3)) if match.group(3) else 0
            z_part = int(match.group(4)) if match.group(4) else 0
            return (d_part, x_part, y_part, z_part, rule)
        return (3, 0, 0, 0, rule)  # 将非标准编号的规则排在后面

    def get_results(self):
        """
        格式化并返回最终的、包含统计和说明的检查结果。
        """
        # 1. 排序原始结果
        sorted_results = {}
        for category, items in self.results.items():
            if isinstance(items, list):
                sorted_results[category] = sorted(items, key=self._sort_key)
            else:
                sorted_results[category] = items

        # 2. 计算统计数据
        stats = {
            "不合格": len(sorted_results.get("不合格", [])),
            "不规范": len(sorted_results.get("不规范", [])),
            "请人工确认": len(sorted_results.get("请人工确认", [])),
        }
        stats["所有不合格、不规范"] = stats["不合格"] + stats["不规范"]

        # 3. 生成简要说明
        summary_parts = []
        if stats["不合格"] > 0:
            summary_parts.append(f"{stats['不合格']}项不合格")
        if stats["不规范"] > 0:
            summary_parts.append(f"{stats['不规范']}项不规范")
        if stats["请人工确认"] > 0:
            summary_parts.append(f"{stats['请人工确认']}项需人工确认")

        if not summary_parts:
            summary_brief = "校对完成，未发现明显问题。"
        else:
            summary_brief = f"校对发现{', '.join(summary_parts)}。"

        # 4. 生成详细说明
        def format_detailed_item(item, index, total_items):
            max_digits = len(str(total_items))
            prefix = f"{index}.".ljust(max_digits + 2)
            indentation = " " * len(prefix)

            details = [f"{prefix}问题描述: {item['问题描述']}"]
            if item.get('规则编号'):
                details.append(f"{indentation}规则编号: {item['规则编号']}")
            if item.get('主要依据'):
                details.append(f"{indentation}主要依据: {item['主要依据']}")
            if item.get('建议操作'):
                details.append(f"{indentation}建议操作: {item['建议操作']}")
            return "\n".join(details)

        detailed_parts = []
        if stats["不合格"] > 0:
            unqualified_list = sorted_results.get("不合格", [])
            total = len(unqualified_list)
            unqualified_items = [format_detailed_item(item, i + 1, total) for i, item in enumerate(unqualified_list)]
            detailed_parts.append("【不合格项】\n" + "\n\n".join(unqualified_items))
        
        if stats["不规范"] > 0:
            non_standard_list = sorted_results.get("不规范", [])
            total = len(non_standard_list)
            non_standard_items = [format_detailed_item(item, i + 1, total) for i, item in enumerate(non_standard_list)]
            detailed_parts.append("【不规范项】\n" + "\n\n".join(non_standard_items))

        if stats["请人工确认"] > 0:
            manual_check_list = sorted_results.get("请人工确认", [])
            total = len(manual_check_list)
            manual_check_items = [format_detailed_item(item, i + 1, total) for i, item in enumerate(manual_check_list)]
            detailed_parts.append("【请人工确认】\n" + "\n\n".join(manual_check_items))

        if not detailed_parts:
            summary_detailed = "所有检查项均符合规范。"
        else:
            summary_detailed = "本次校对的详细结果如下：\n\n" + "\n\n".join(detailed_parts)

        # 5. 组装最终返回结果
        final_report = {
            "ticket_id": self.ticket_id,
            "check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "stats": stats,
            "rule_execution_stats": self.rule_stats,
            "summary_brief": summary_brief,
            "summary_detailed": summary_detailed,
            "results": sorted_results,
        }
        
        return final_report
