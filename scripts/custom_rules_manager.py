import json
import logging
import os
from typing import List, Dict

class CustomRuleManager:
    def __init__(self, rules_file: str = 'custom_rules.json'):
        self.rules_file = rules_file
        self.rules: List[Dict] = self._load_rules()

    def _load_rules(self) -> List[Dict]:
        if os.path.exists(self.rules_file):
            try:
                with open(self.rules_file, 'r', encoding='utf-8') as f:
                    rules = json.load(f)
                    if not isinstance(rules, list):
                        raise ValueError("Rules file must contain a list of rule objects.")
                    return rules
            except (json.JSONDecodeError, ValueError) as e:
                logging.error(f"Error loading custom rules: {e}")
                return []
        return []

    def save_rules(self):
        with open(self.rules_file, 'w', encoding='utf-8') as f:
            json.dump(self.rules, f, ensure_ascii=False, indent=2)

    def add_rule(self, rule: Dict) -> Dict:
        # 验证规则结构
        required_keys = {'rule_id', 'category', 'field', 'condition', 'description', 'evidence_template', 'suggestion'}
        if not all(key in rule for key in required_keys):
            raise ValueError(f"Rule missing required keys: {required_keys - set(rule.keys())}")
        
        # 支持的condition: 'not_empty', 'matches_regex', 'is_datetime', 'custom_lambda' (但custom_lambda有风险，仅限简单表达式)
        if rule['condition'] not in ['not_empty', 'matches_regex', 'is_datetime']:
            raise ValueError("Unsupported condition. Supported: 'not_empty', 'matches_regex', 'is_datetime'")
        
        # 检查rule_id唯一性
        if any(r['rule_id'] == rule['rule_id'] for r in self.rules):
            raise ValueError(f"Rule ID '{rule['rule_id']}' already exists.")
        
        self.rules.append(rule)
        self.save_rules()
        return rule

    def get_rules(self) -> List[Dict]:
        return self.rules

    def get_rule_by_id(self, rule_id: str) -> Dict | None:
        return next((r for r in self.rules if r['rule_id'] == rule_id), None)

    def delete_rule(self, rule_id: str):
        self.rules = [r for r in self.rules if r['rule_id'] != rule_id]
        self.save_rules()

    def update_rule(self, rule_id: str, updated_rule: Dict):
        for i, r in enumerate(self.rules):
            if r['rule_id'] == rule_id:
                self.rules[i].update(updated_rule)
                self.save_rules()
                return self.rules[i]
        raise ValueError(f"Rule ID '{rule_id}' not found.")