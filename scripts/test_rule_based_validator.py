#!/usr/bin/env python3
"""
测试基于规则文件的校验器
"""

import json
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.rule_based_validator import RuleBasedValidator
from scripts.rule_check import WorkTicketValidator
from scripts.ticket_parser import TicketParser

def compare_results(old_results, new_results):
    """比较新旧校验结果"""
    print("\n" + "="*60)
    print("校验结果对比")
    print("="*60)
    
    # 比较统计数据
    old_stats = old_results.get('stats', {})
    new_stats = new_results.get('stats', {})
    
    print(f"\n统计对比:")
    print(f"{'类别':<15} {'原版':<10} {'新版':<10} {'差异':<10}")
    print("-" * 50)
    
    for category in ['不合格', '不规范', '请人工确认']:
        old_count = old_stats.get(category, 0)
        new_count = new_stats.get(category, 0)
        diff = new_count - old_count
        diff_str = f"+{diff}" if diff > 0 else str(diff) if diff < 0 else "0"
        print(f"{category:<15} {old_count:<10} {new_count:<10} {diff_str:<10}")
    
    # 显示规则执行统计
    if 'rule_execution_stats' in new_results:
        rule_stats = new_results['rule_execution_stats']
        print(f"\n新版规则执行统计:")
        print(f"  总规则数: {rule_stats['total']}")
        print(f"  通过: {rule_stats['passed']}")
        print(f"  不通过: {rule_stats['failed']}")
        print(f"  跳过: {rule_stats['skipped']}")
        print(f"  错误: {rule_stats['error']}")

def test_with_sample_data():
    """使用示例数据测试"""
    print("使用示例数据测试...")
    
    # 创建测试数据
    test_data = {
        "编号": "ZD-TX20231204001",
        "工作票类型": "",  # 故意留空测试
        "工作任务": "系统升级维护",
        "工作地点": "主控室",
        "计划工作时间": {
            "开始时间": "2024-12-01 09:00",
            "结束时间": "2024-12-01 17:00"
        },
        "工作许可": {
            "许可时间": "",  # 故意留空测试
            "工作许可人签名": "张三",
            "工作负责人签名": "李四"
        },
        "签发": {
            "工作票签发人签名": "王五",
            "工作票会签人签名": ""  # 故意留空测试双签发
        },
        "工作票终结": {
            "描述": "工作已完成",
            "工作负责人签名": "李四",
            "工作许可人签名": "张三"
        },
        "工作班人员（不包括工作负责人）": [
            {"姓名": "赵六"},
            {"姓名": "钱七"}
        ],
        "工作负责人及工作班人员总数": "3",
        "工作负责人（监护人）": "李四",
        "工作要求的安全措施": {
            "硬件及工作环境,应设遮栏、应挂标示牌位置": "现场无需特殊措施",  # 故意不包含关键词
            "软件及数据": "操作前进行系统备份，制定回退方案",
            "附件": "无"
        },
        "安全交代": {
            "交代时间": "2024-12-01 08:30",
            "工作班人员签名": ["赵六", "钱七"]
        }
    }
    
    return test_data

def test_with_html_file(html_file):
    """使用HTML文件测试"""
    print(f"使用HTML文件测试: {html_file}")
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except FileNotFoundError:
        print(f"错误: 测试文件 '{html_file}' 未找到。")
        return None

    # 解析HTML
    parser = TicketParser()
    ticket_data, parsing_errors = parser.parse(html_content)

    if parsing_errors:
        print("\n解析时发生错误:")
        for error in parsing_errors:
            print(f"  {error}")
    
    return ticket_data

def main():
    """主测试函数"""
    print("基于规则文件的校验器测试")
    print("="*60)
    
    # 选择测试数据源
    use_html = len(sys.argv) > 1
    
    if use_html:
        html_file = sys.argv[1]
        ticket_data = test_with_html_file(html_file)
        if ticket_data is None:
            return
    else:
        ticket_data = test_with_sample_data()
    
    print(f"\n工作票ID: {ticket_data.get('编号', '未知')}")
    
    # 测试原版校验器
    print("\n" + "-"*40)
    print("执行原版校验器...")
    print("-"*40)
    
    old_validator = WorkTicketValidator(ticket_data)
    old_results = old_validator.validate()
    
    # 测试新版校验器
    print("\n" + "-"*40)
    print("执行新版规则校验器...")
    print("-"*40)
    
    new_validator = RuleBasedValidator(ticket_data)
    new_results = new_validator.validate()
    
    # 对比结果
    compare_results(old_results, new_results)
    
    # 输出详细结果
    print(f"\n" + "="*60)
    print("新版校验器详细结果")
    print("="*60)
    print(f"\n{new_results['summary_brief']}")
    print(f"\n{new_results['summary_detailed']}")
    
    # 保存结果到文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    with open(f'test_results_old_{timestamp}.json', 'w', encoding='utf-8') as f:
        json.dump(old_results, f, ensure_ascii=False, indent=2)
    
    with open(f'test_results_new_{timestamp}.json', 'w', encoding='utf-8') as f:
        json.dump(new_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到:")
    print(f"  原版: test_results_old_{timestamp}.json")
    print(f"  新版: test_results_new_{timestamp}.json")

if __name__ == '__main__':
    main()
