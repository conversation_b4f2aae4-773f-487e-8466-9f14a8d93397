import requests
import logging
import json
import re
from .config import Config
from .prompt_template import GENERATE_PROMPT


class LLMClient:
    def __init__(self):
        """
        初始化 LLM 客户端，加载配置和规则文件。
        
        """
        # 加载配置
        config = Config()
        self.url = config.get('llm_url')
        self.model = config.get('llm_model', 'qwen2.5-32b')  # 提供默认值
        self.api_key = config.get('llm_api_key')
        self.rules_file_path = config.get('rules_file_path')  # 允许通过配置覆盖默认路径

        if not self.url:
            raise ValueError("LLM URL not configured in config.yaml")

        # 读取规则文件
        try:
            with open(self.rules_file_path, 'r', encoding='utf-8') as f:
                self.rules_content = f.read()
        except FileNotFoundError:
            logging.error(f"Rules file '{self.rules_file_path}' not found")
            self.rules_content = ""
            raise ValueError(f"Rules file '{self.rules_file_path}' not found")
        except Exception as e:
            logging.error(f"Error reading rules file: {e}")
            self.rules_content = ""
            raise ValueError(f"Error reading rules file: {e}")

        # 初始化日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def query(self, prompt: str, max_tokens: int = 131072) -> str:
        """
        调用 LLM API，发送 Prompt 并获取响应。
        
        :param prompt: 输入的 Prompt 字符串
        :param max_tokens: 最大输出 token 数，默认为 1000
        :return: LLM 的响应内容（字符串）
        """
        headers = {
            'Content-Type': 'application/json',
        }
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'

        data = {
            'model': self.model,
            'messages': [
                {'role': 'user', 'content': prompt}
            ],
            'max_tokens': max_tokens,
            'temperature': 0.5,
        }
        
        try:
            # self.logger.info(f"Request data: {data}")
            response = requests.post(self.url, headers=headers, json=data, timeout=999)
            self.logger.info(f"Response status: {response.status_code}")
            self.logger.info(f"Response headers: {response.headers}")
            
            response.raise_for_status()
            result = response.json()
            # self.logger.info(f"Response JSON: {result}")
            
            content = result['choices'][0]['message']['content'].strip()
            self.logger.info(f"Extracted content: '{content}'")
            return content
        except requests.RequestException as e:
            self.logger.error(f"Error querying LLM: {e}")
            return f"LLM query failed: {str(e)}"


    def validate_ticket(self, ticket_data: dict, manual_items: list) -> dict:
        """
        使用 LLM 对工作票进行全面校核，基于规则文件和票面数据。
        
        :param ticket_data: 工作票解析数据（字典）
        :return: 结构化的校核结果，包含 Prompt、原始响应和解析后的结果
        """
        try:
            # 1. 生成 Prompt
            ticket_data_str = json.dumps(ticket_data, ensure_ascii=False, indent=2)
            prompt = GENERATE_PROMPT.format(ticket_data=ticket_data_str, manual_items=manual_items, rules_file=self.rules_content)

            # 2. 调用 LLM
            llm_response = self.query(prompt)

            # 3. 解析 LLM 响应
            # llm_response = self._parse_llm_response(llm_response)

            # 4. 返回结构化结果
            return {
                'prompt': prompt,
                'response': llm_response,
            }

        except Exception as e:
            self.logger.error(f"Error during LLM validation: {e}")
            return {
                'prompt': '',
                'response': f"LLM validation failed: {str(e)}",
            }

    def _parse_llm_response(self, response: str) -> list:
        """
        Parse LLM response into structured format.
        """
        try:
            # Try to parse JSON response
            parsed = json.loads(response)
            if not isinstance(parsed, list):
                raise ValueError("Response must be a list of rule evaluations")
            
            for item in parsed:
                if not all(key in item for key in ["规则编号", "判断", "理由"]):
                    raise ValueError("Each item must contain 规则编号, 判断, and 理由")
                if item["判断"] not in ["合规", "不合规", "不规范", "不确定"]:
                    raise ValueError("判断 must be one of: 合规, 不合规, 不规范, 不确定")
            return parsed
        except json.JSONDecodeError:
            # Fallback parsing if response isn't valid JSON
            results = []
            for line in response.split('\n'):
                if line.strip().startswith('规则'):
                    parts = line.split(':', 1)[1].split('-', 1)
                    if len(parts) == 2:
                        rule_id, rest = parts
                        rule_id = rule_id.strip()
                        judgment, reason = rest.split(':', 1) if ':' in rest else (rest.strip(), '')
                        results.append({
                            "规则编号": rule_id,
                            "判断": judgment.strip(),
                            "理由": reason.strip()
                        })
            return results