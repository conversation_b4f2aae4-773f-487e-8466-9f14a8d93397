from datetime import datetime
import re
from collections import defaultdict
from .custom_rules_manager import CustomRuleManager
from .llm_client import LLMClient
from .config import Config

class WorkTicketValidator:
    """
    工作票自动校对器。
    根据《附录D 工作票不合格、不规范判据》对解析后的工单数据进行自动化检查。
    """
    def __init__(self, ticket_data):
        """
        初始化检查器。
        :param ticket_data: 从 ticket_parser.py 解析出的字典数据。
        """
        self.ticket = ticket_data if isinstance(ticket_data, dict) else {}
        self.results = defaultdict(list)
        self.ticket_id = self.get_nested_value("编号", "未知编号")

    def get_nested_value(self, field_path: str, default=None):
        """
        安全地从嵌套字典中获取值。
        例如: "工作许可.许可时间"
        """
        keys = field_path.split('.')
        value = self.ticket
        for key in keys:
            if isinstance(value, dict):
                value = value.get(key)
            elif isinstance(value, list) and key.isdigit() and int(key) < len(value):
                value = value[int(key)]
            else:
                return default
            if value is None:
                return default
        return value

    def _add_result(self, category: str, rule: str, description: str, evidence: str = "", suggestion: str = ""):
        """
        添加一条结构化的检查结果。
        - category: '不合格', '不规范', '请人工确认'
        - 规则编号: 规则编号, e.g., 'D1-2'
        - 问题描述: 对问题的简要描述
        - 主要依据: 支持该结论的具体票面数据
        - 建议操作: 建议的后续操作
        """
        result_entry = {
            "规则编号": rule,
            "问题描述": description,
            "主要依据": evidence,
            "建议操作": suggestion
        }
        # 防止重复添加完全相同的结果
        if result_entry not in self.results[category]:
            self.results[category].append(result_entry)

    def validate(self):
        """
        执行所有检查并返回结果。
        """
        if not self.ticket:
            self._add_result("不合格", "FATAL", "输入的工作票数据为空或格式错误。", 
                             "传入的 ticket_data 为空。", "请检查上游解析流程是否正确。")
            return self.get_results()

        # --- 不合格项检查 (D1) ---
        self.check_d1_1_type()
        self.check_d1_2_time_limit()
        self.check_d1_3_keywords()
        self.check_d1_5_team_mismatch()
        self.check_d1_6_content_clarity()
        self.check_d1_7_safety_measures()
        self.check_d1_8_dual_signature()
        self.check_d1_9_permit_time()
        self.check_d1_10_owner_change()
        self.check_d1_11_extension_interruption()
        self.check_d1_12_add_task()
        self.check_d1_13_termination()
        self.check_d1_16_signatures()

        # --- 不规范项检查 (D2) ---
        self.check_d2_2_ticket_number()
        self.check_d2_6_time_format()

        # --- 需人工确认项 ---
        self.add_manual_checks()

        # # 新增: 加载自定义规则并执行
        # self.apply_custom_rules()

        # # 新增: 使用大模型处理
        # self.enhance_with_llm()

        return self.get_results()

    @staticmethod
    def _sort_key(result_item):
        """为结果条目生成排序键，确保按规则编号 D-X-Y(Z) 排序。"""
        rule = result_item.get('规则编号', '')
        match = re.match(r'D(\d+)-(\d+)(?:\((\d+)\))?', rule)
        if match:
            d_part = int(match.group(1))
            x_part = int(match.group(2))
            y_part = int(match.group(3)) if match.group(3) else 0
            return (d_part, x_part, y_part, rule)
        return (3, 0, 0, rule) # 将非标准编号的规则排在后面

    def apply_custom_rules(self):
        config = Config()
        rules_file = config.get('custom_rules_file', 'custom_rules.json')
        manager = CustomRuleManager(rules_file)
        custom_rules = manager.get_rules()

        for rule in custom_rules:
            field_value = self.get_nested_value(rule['field'])
            category = rule['category']
            rule_id = rule['rule_id']
            description = rule['description']
            evidence = rule['evidence_template'].format(value=field_value)
            suggestion = rule['suggestion']

            violated = False
            if rule['condition'] == 'not_empty':
                violated = not field_value
            elif rule['condition'] == 'matches_regex':
                regex = rule.get('regex')
                if regex and field_value:
                    violated = not re.match(regex, str(field_value))
            elif rule['condition'] == 'is_datetime':
                if field_value:
                    try:
                        datetime.strptime(field_value, "%Y-%m-%d %H:%M")
                    except ValueError:
                        violated = True

            if violated:
                self._add_result(category, rule_id, description, evidence, suggestion)

    def enhance_with_llm(self):
        if not self.results.get('请人工确认'):
            return  # 无需LLM处理

        llm = LLMClient()

        # 构建prompt: 汇总所有"请人工确认"项，并提供票面数据摘要
        manual_items = self.results['请人工确认']
        llm_result = llm.validate_ticket(self.ticket, manual_items)

        # 将LLM响应添加到报告中
        self.results['llm_analysis'] = llm_result
 
    def get_results(self):
        """
        格式化并返回最终的、包含统计和说明的检查结果。
        """
        # 1. 排序原始结果
        sorted_results = {}
        for category, items in self.results.items():
            if isinstance(items, list):
                sorted_results[category] = sorted(items, key=self._sort_key)
            else:
                sorted_results[category] = items

        # 2. 计算统计数据
        stats = {
            "不合格": len(sorted_results.get("不合格", [])),
            "不规范": len(sorted_results.get("不规范", [])),
            "请人工确认": len(sorted_results.get("请人工确认", [])),
        }
        stats["所有不合格、不规范"] = stats["不合格"] + stats["不规范"]

        # 3. 生成简要说明
        summary_parts = []
        if stats["不合格"] > 0:
            summary_parts.append(f"{stats['不合格']}项不合格")
        if stats["不规范"] > 0:
            summary_parts.append(f"{stats['不规范']}项不规范")
        if stats["请人工确认"] > 0:
            summary_parts.append(f"{stats['请人工确认']}项需人工确认")

        if not summary_parts:
            summary_brief = "校对完成，未发现明显问题。"
        else:
            summary_brief = f"校对发现{', '.join(summary_parts)}。"

        # 4. 生成详细说明
        def format_detailed_item(item, index, total_items):
            # 根据项目总数确定序号的最大位数，用于对齐
            max_digits = len(str(total_items))
            # 使用 ljust 进行左对齐填充，使 "1." 和 "10." 占用的宽度相同
            prefix = f"{index}.".ljust(max_digits + 2)
            # 后续行的缩进与前缀的宽度保持一致，确保所有标签文本左侧对齐
            indentation = " " * len(prefix)

            details = [f"{prefix}问题描述: {item['问题描述']}"]
            if item.get('规则编号'):
                details.append(f"{indentation}规则编号: {item['规则编号']}")
            if item.get('主要依据'):
                details.append(f"{indentation}主要依据: {item['主要依据']}")
            if item.get('建议操作'):
                details.append(f"{indentation}建议操作: {item['建议操作']}")
            return "\n".join(details)

        detailed_parts = []
        if stats["不合格"] > 0:
            unqualified_list = sorted_results.get("不合格", [])
            total = len(unqualified_list)
            unqualified_items = [format_detailed_item(item, i + 1, total) for i, item in enumerate(unqualified_list)]
            detailed_parts.append("【不合格项】\n" + "\n\n".join(unqualified_items))
        
        if stats["不规范"] > 0:
            non_standard_list = sorted_results.get("不规范", [])
            total = len(non_standard_list)
            non_standard_items = [format_detailed_item(item, i + 1, total) for i, item in enumerate(non_standard_list)]
            detailed_parts.append("【不规范项】\n" + "\n\n".join(non_standard_items))

        if stats["请人工确认"] > 0:
            manual_check_list = sorted_results.get("请人工确认", [])
            total = len(manual_check_list)
            manual_check_items = [format_detailed_item(item, i + 1, total) for i, item in enumerate(manual_check_list)]
            detailed_parts.append("【请人工确认】\n" + "\n\n".join(manual_check_items))

        if not detailed_parts:
            summary_detailed = "所有检查项均符合规范。"
        else:
            summary_detailed = "本次校对的详细结果如下：\n\n" + "\n\n".join(detailed_parts)

        # 5. 组装最终返回结果
        final_report = {
            "ticket_id": self.ticket_id,
            "check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "stats": stats,
            "summary_brief": summary_brief,
            "summary_detailed": summary_detailed,
            "results": sorted_results,
        }
        
        return final_report

    # --- 不合格项检查方法 (D1) ---

    def check_d1_1_type(self):
        ticket_type = self.get_nested_value("工作票类型", "")
        task = self.get_nested_value("工作任务", "")
        if not ticket_type:
            self._add_result("不合格", "D1-1", "工作票类型漏填。", 
                             "“工作票类型”字段为空。", "请填写正确的工作票类型。")
        elif "紧急抢修" in ticket_type: # TODO
            self._add_result("请人工确认", "D1-1", "请确认是否错用调度机构主站端紧急抢修工作票。",
                             f"当前工作票类型为“{ticket_type}”。",
                             "请根据实际工作性质，判断使用“紧急抢修工作票”是否恰当。")
        elif "调度机构主站端工作票" in ticket_type and any(keyword in task for keyword in ["紧急", "紧急消缺"]):
            self._add_result("不合格", "D1-1", "错用工作票类型，紧急工作应使用紧急抢修工作票。",
                             f"当前工作票类型为“{ticket_type}”，但工作任务中包含“紧急”或“紧急消缺”字样:“{task}”。",
                             "请重新填写并提交调度机构主站端紧急抢修工作票。")
        
    def check_d1_2_time_limit(self):
        start_str = self.get_nested_value("计划工作时间.开始时间")
        end_str = self.get_nested_value("计划工作时间.结束时间")
        
        if not start_str or not end_str:
            self._add_result("不合格", "D1-2", "计划工作起止时间漏填。",
                             f"解析结果: 开始时间='{start_str or '空'}', 结束时间='{end_str or '空'}'。",
                             "请完整填写计划工作的开始和结束时间。")
            return

        try:
            start = datetime.strptime(start_str, "%Y-%m-%d %H:%M")
            end = datetime.strptime(end_str, "%Y-%m-%d %H:%M")
            now = datetime.now()
            
            if end < now:
                self._add_result("不合格", "D1-2", "计划工作时间已过期。",
                                 f"计划结束时间 ({end.strftime('%Y-%m-%d %H:%M')}) 早于当前时间 ({now.strftime('%Y-%m-%d %H:%M')})。",
                                 "请修改计划工作时间或作废此票。")
            if start >= end:
                self._add_result("不合格", "D1-2", "计划开始时间晚于或等于结束时间。",
                                 f"开始时间 ({start_str}) vs 结束时间 ({end_str})。",
                                 "请确保计划开始时间早于结束时间。")
        except (ValueError, TypeError):
            self._add_result("不合格", "D1-2", "计划工作时间格式无法解析。",
                             f"解析出的值为：开始='{start_str}', 结束='{end_str}'。",
                             "请按“年-月-日 时:分”格式填写。")

    def check_d1_3_keywords(self):
        # D1-3(4): 检查关键时间
        permit_time = self.get_nested_value("工作许可.许可时间")
        if not permit_time:
            self._add_result("不合格", "D1-3(4)", "关键词“工作许可时间”漏填。",
                             "“工作许可.许可时间”字段为空。", "请工作许可人填写许可时间并签名。")
        elif not re.search(r'\d{1,2}\s*月\s*\d{1,2}\s*日\s*\d{1,2}\s*时\s*\d{1,2}\s*分', permit_time):
            if re.search(r'\d', permit_time):
                self._add_result("不合格", "D1-3(4)", "关键词“工作许可时间”填写不规范或不清。",
                                 f"工作许可.许可时间为：“{permit_time}”，其中的时间信息格式不标准。", "请核对并确保工作许可时间清晰、完整。")
            else:
                self._add_result("不合格", "D1-3(4)", "关键词“工作许可时间”漏填。",
                                 f"工作许可.许可时间为：“{permit_time}”，其中未找到任何时间信息。", "请在工作许可时填写许可时间。")
        
        term_desc = self.get_nested_value("工作票终结.描述", "")
        if not term_desc:
            self._add_result("不合格", "D1-3(4)", "关键词“工作终结时间”漏填。",
                             "“工作票终结.描述”字段为空，无法提取终结时间。", "请在工作终结时填写完整的终结信息，包括终结时间。")
        elif not re.search(r'\d{1,2}\s*月\s*\d{1,2}\s*日\s*\d{1,2}\s*时\s*\d{1,2}\s*分', term_desc):
            if re.search(r'\d', term_desc):
                self._add_result("不合格", "D1-3(4)", "关键词“工作终结时间”疑似填写不规范或不清。",
                                 f"终结描述为：“{term_desc}”，其中的时间信息格式不标准。", "请核对并确保终结时间清晰、完整。")
            else:
                self._add_result("不合格", "D1-3(4)", "关键词“工作终结时间”漏填。",
                                 f"在终结描述“{term_desc}”中未找到任何时间信息。", "请在工作终结时填写完整的终结信息，包括终结时间。")

    def check_d1_5_team_mismatch(self):
        members = self.get_nested_value("工作班人员（不包括工作负责人）", [])
        claimed_count_str = self.get_nested_value("工作负责人及工作班人员总数", "0")
        leader = self.get_nested_value("工作负责人（监护人）", "未知")
        
        actual_count = len(members) + 1
        
        claimed_count = 0
        if claimed_count_str:
            match = re.search(r'\d+', str(claimed_count_str))
            if match:
                claimed_count = int(match.group())
        
        if claimed_count != actual_count:
            member_names = [m.get('姓名', '未知') for m in members]
            self._add_result("不合格", "D1-5", "工作班人数与申报总数不符。",
                             f"申报总人数为 {claimed_count}，但实际人数为 {actual_count} (负责人: {leader}, 成员: {member_names})。",
                             "请核对工作班成员名单及申报总数，确保一致。")

    def check_d1_6_content_clarity(self):
        fields_to_check = {"工作任务": "工作任务", "工作地点": "工作地点"}
        for field_path, name in fields_to_check.items():
            value = self.get_nested_value(field_path, "").strip()
            if not value or value == "无":
                self._add_result("不合格", "D1-6", f"{name}填写不明确或错漏。",
                                 f"字段“{name}”内容为空或为“无”。", f"请详细、明确地填写{name}。")
            elif any(k in value for k in ["见", "附", "详见"]):
                self._add_result("请人工确认", "D1-6", f"{name}可能引用了外部文档。",
                                 f"{name}填写为“{value}”。", "请核对可能所引用的附件或说明，确认其内容是否明确、完备。")

    def check_d1_7_safety_measures(self):
        measures = self.get_nested_value("工作要求的安全措施", {})
        hw = measures.get("硬件及工作环境,应设遮栏、应挂标示牌位置", "")
        sw = measures.get("软件及数据", "")
        attachment = measures.get("附件", "")
        task = self.get_nested_value("工作任务", "")

        if attachment and not any(k in attachment for k in ["无", "不涉及", "空"]):
            self._add_result("请人工确认", "D1-7", "安全措施可能指向外部附件，需人工核对。",
                             f"附件栏填写为：“{attachment}”。", "请仔细核对附件内容，确保其完备、正确，并满足所有相关安全要求。")
            return

        # D1-7(1): 检查硬件安措关键词
        if not any(k in hw for k in ["遮栏", "标示牌", "标志牌", "警示带", "误碰", "隔离"]):
            self._add_result("不合格", "D1-7(1)", "硬件安全措施可能不完备，缺少关键措施。",
                             f"硬件安措内容为：“{hw}”，未找到“遮栏”、“标示牌”、“误碰”等关键词。",
                             "请根据现场情况，补充必要的硬件安全措施。")
        
        # D1-7(4) & (5): 检查软件安措
        # 识别是否为软件相关操作
        is_sw_operation = any(k in task for k in ["软件", "数据库", "系统", "固件", "配置", "功能", "数据", "网络", "升级", "变更"])
        if is_sw_operation:
            if "备份" not in sw:
                self._add_result("不合格", "D1-7(4)", "软件操作未填写“备份”措施。",
                                 f"工作任务“{task}”涉及软件操作，但软件安措“{sw}”中未提及“备份”。",
                                 "请在操作前增加数据和系统备份措施。")
            if "测试" not in sw:
                self._add_result("不合格", "D1-7(5)", "软件操作未填写“测试”措施。",
                                 f"工作任务“{task}”涉及软件操作，但软件安措“{sw}”中未提及“测试”。",
                                 "请补充说明功能变更是否在备用或测试系统上测试。")
            if not any(k in sw for k in ["异常", "回退", "恢复"]):
                self._add_result("不合格", "D1-7(5)", "软件操作未填写异常情况处理方法。",
                                 f"工作任务“{task}”涉及软件操作，但软件安措“{sw}”中未找到“异常处理”、“回退”、“恢复”等关键词。",
                                 "请补充出现异常情况后的具体处理或回退方案。")

    def check_d1_8_dual_signature(self):
        ticket_type = self.get_nested_value("工作票类型", "")
        issuer = self.get_nested_value("签发.工作票签发人签名")
        countersigner = self.get_nested_value("签发.工作票会签人签名")
        
        if not issuer or not countersigner:
            self._add_result("不合格", "D1-8", "此工作票应“双签发”而未“双签发”。",
                                f"票据类型为“{ticket_type}”，要求双签发。当前状态：签发人“{issuer or '未签'}”，会签人“{countersigner or '未签'}”。",
                                "请工作票签发人和会签人共同签名。")

    def check_d1_9_permit_time(self):
        if not self.get_nested_value("工作许可.许可时间"):
            self._add_result("不合格", "D1-9", "工作许可时间未填写。", "“工作许可.许可时间”字段为空。", "请工作许可人填写许可时间。")

    def check_d1_10_owner_change(self):
        change = self.get_nested_value("工作变更.工作负责人变更", {})
        if change and any(change.values()):
            required = ["工作票签发人签名", "同意变更时间", "原工作负责人签名", "现工作负责人签名", "工作许可人签名", "许可时间"]
            missing = [field for field in required if not change.get(field)]
            if missing:
                self._add_result("不合格", "D1-10", "工作负责人变更手续不完整。",
                                 f"变更记录中，以下字段缺失：{', '.join(missing)}。", "请补全所有变更手续和签名。")

    def check_d1_11_extension_interruption(self):
        # 检查延期
        ext = self.get_nested_value("工作延期", {})
        if ext and any(ext.values()):
            required = ["有效期延长到", "工作许可人签名", "工作负责人签名", "延期时间"]
            missing = [field for field in required if not ext.get(field)]
            if missing:
                self._add_result("不合格", "D1-11", "工作延期手续不完整。",
                                 f"延期记录中，以下字段缺失：{', '.join(missing)}。", "请补全所有延期手续和签名。")
        # 检查间断
        interruptions = self.get_nested_value("工作间断", [])
        if interruptions:
            for i, record in enumerate(interruptions):
                required = ["工作间断时间", "工作许可人（间断）", "工作负责人（间断）", "工作开工时间", "工作许可人（开工）", "工作负责人（开工）"]
                missing = [field for field in required if not record.get(field)]
                if missing:
                    self._add_result("不合格", "D1-11", f"第 {i+1} 条工作间断记录不全。",
                                     f"该条记录缺少字段：{', '.join(missing)}。", "请补全该条工作间断的所有信息和签名。")

    def check_d1_12_add_task(self):
        change = self.get_nested_value("工作变更.工作任务变更", {})
        added_content = change.get("增加的工作内容")
        permit_sig = change.get("工作许可人签名")
        if added_content:
            if not permit_sig:
                self._add_result("不合格", "D1-12", "增加工作内容但未经许可人签名同意。",
                                 f"增加内容为“{added_content}”，但相应的工作许可人签名字段为空。",
                                 "请工作许可人确认增加内容并签名。")
            
            # 根据用户反馈，增加对“是否真的不需要变更安措”的确认
            self._add_result("请人工确认", "D1-12", "请确认：增加的工作内容是否确实无需变更安全措施。",
                             f"票面在“不需变更安全措施下增加的工作内容”一栏填写了：“{added_content}”。",
                             "请专家结合原始安措和工作任务判断，若此新增内容实际上需要变更安措，则应重新办理工作票，当前操作不合规。")

    def check_d1_13_termination(self):
        term = self.get_nested_value("工作票终结", {})
        missing = []
        if not term.get("描述"): missing.append("终结描述")
        if not term.get("工作负责人签名"): missing.append("工作负责人签名")
        if not term.get("工作许可人签名"): missing.append("工作许可人签名")
        
        if missing:
             self._add_result("不合格", "D1-13", "工作终结栏内容填写不完整。",
                              f"缺少 {', '.join(missing)}。", "请在工作终结后，由相关人员补全所有信息和签名。")

    def check_d1_16_signatures(self):
        # 检查关键角色签名
        required_signatures = {
            "工作负责人（监护人）": "工作负责人（监护人）",
            "签发.工作票签发人签名": "工作票签发人",
            "工作许可.工作许可人签名": "工作许可人",
            "工作许可.工作负责人签名": "工作负责人（许可时）",
            "工作票终结.工作负责人签名": "工作负责人（终结时）",
            "工作票终结.工作许可人签名": "工作许可人（终结时）",
        }
        for field_path, name in required_signatures.items():
            if not self.get_nested_value(field_path):
                self._add_result("不合格", "D1-16", f"存在漏签名：{name}未签名。",
                                 f"“{field_path}”字段为空。", f"请 {name} 签名。")

        # 检查安全交代签名人数
        briefing_sigs = self.get_nested_value("安全交代.工作班人员签名", [])
        team_members = self.get_nested_value("工作班人员（不包括工作负责人）", [])
        if team_members and not briefing_sigs:
            self._add_result("不合格", "D1-16", "安全交代中工作班人员全体漏签名。",
                             f"工作班有 {len(team_members)} 人，但安全交代中无任何签名。", "请全体工作班成员在安全交代后签名。")
        elif team_members and len(briefing_sigs) != len(team_members):
             self._add_result("不合格", "D1-16", "安全交代签名人数与工作班人数不符。",
                              f"工作班有 {len(team_members)} 人，但安全交代中只有 {len(briefing_sigs)} 个签名。", "请确保所有工作班成员都已签名。")

    # --- 不规范项检查方法 (D2) ---

    def check_d2_2_ticket_number(self):
        ticket_no = self.get_nested_value("编号")
        if not ticket_no:
            self._add_result("不规范", "D2-2", "工作票编号漏填。", "“编号”字段为空。", "请填写正确的工单编号。")
        # 格式示例：ZD-TX20231204001
        elif not re.match(r'^[A-Z]{2}-[A-Z]{2}\d{11}$', ticket_no):
             self._add_result("不规范", "D2-2", "工作票编号格式不正确。",
                              f"编号“{ticket_no}”不符合 'XX-XXYYYYMMDDXXX' 格式。", "请按规定格式修改编号。")

    def check_d2_6_time_format(self):
        time_fields = {
            "计划工作时间.开始时间": "计划开始时间", "计划工作时间.结束时间": "计划结束时间",
            "签发.工作票签发时间": "签发时间", "签发.工作票会签时间": "会签时间",
            "工作许可.许可时间": "许可时间", "安全交代.交代时间": "安全交代时间",
            "工作延期.有效期延长到": "延期后有效期", "工作延期.延期时间": "延期办理时间",
            "工作变更.工作任务变更.时间": "工作任务变更时间",
            "工作变更.工作负责人变更.同意变更时间": "负责人变更同意时间",
            "工作变更.工作负责人变更.许可时间": "负责人变更许可时间",
        }
        for field, name in time_fields.items():
            value = self.get_nested_value(field)
            # 如果值存在（不为空字符串）且格式不正确，则报告。
            if value and not re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$', str(value)):
                self._add_result("不规范", "D2-6", f"时间“{name}”未按24小时制或标准格式填写。",
                                 f"解析出的值为：“{value}”。", "所有时间均应采用“YYYY-MM-DD HH:MM”格式。")
        
        # 检查列表型时间字段：工作间断
        for i, item in enumerate(self.get_nested_value("工作间断", [])):
            for k, v in item.items():
                if "时间" in k and v and not re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$', str(v)):
                    self._add_result("不规范", "D2-6", f"时间“工作间断第{i+1}条-{k}”格式不规范。", f"值为：“{v}”。", "请使用24小时制标准格式。")

        # 检查列表型时间字段：工作班成员变更
        team_changes = self.get_nested_value("工作变更.工作班成员变更", [])
        for i, item in enumerate(team_changes):
            value = item.get("变更时间")
            if value and not re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$', str(value)):
                self._add_result("不规范", "D2-6", f"时间“工作班成员变更第{i+1}条-变更时间”格式不规范。", f"值为：“{value}”。", "请使用24小时制标准格式。")

    def _collect_all_signatures(self):
        """辅助函数：收集票面所有签名并返回一个字典。"""
        signatures = {}
        sig_paths = {
            "工作负责人（监护人）": "工作负责人（监护人）",
            "签发.工作票签发人签名": "签发人",
            "签发.工作票会签人签名": "会签人",
            "工作许可.工作许可人签名": "许可人",
            "工作许可.工作负责人签名": "负责人(许可时)",
            "安全交代.工作班人员签名": "安全交代(班员)",
            "工作票终结.工作负责人签名": "负责人(终结时)",
            "工作票终结.工作许可人签名": "许可人(终结时)",
        }
        for path, role in sig_paths.items():
            value = self.get_nested_value(path)
            if value:
                if isinstance(value, list):
                    # 处理字典列表（如带单位的签名）和纯字符串列表
                    if value and isinstance(value[0], dict):
                        names = [item.get('姓名', '未知') for item in value]
                        signatures[role] = ", ".join(names)
                    else:
                        signatures[role] = ", ".join(filter(None, value))
                else:
                    signatures[role] = value
        return signatures

    # --- 添加所有需要人工确认的检查项 ---
    
    def add_manual_checks(self):
        task = self.get_nested_value('工作任务', '')
        location = self.get_nested_value('工作地点', '')
        hw = self.get_nested_value("工作要求的安全措施.硬件及工作环境,应设遮栏、应挂标示牌位置", "")

        # D1-3: 关键词错漏
        self._add_result("请人工确认", "D1-3(1,2,3)", "请核对关键词是否错漏。",
                         f"工作任务：“{task}”；工作地点：“{location}”。",
                         "请重点检查下列关键词是否错漏：1. 有关调度机构主站系统和功能模块的具体设备名称、运行进程， 要具体描述，写明设备名、编号等，如“210机房III区B07机柜”；2. “运行、退出、插入、拔出、悬挂”等操作词，工作任务中写明具体要进行的操作；3.有关设备编号的阿拉伯数字，甲、乙，一、二，Ⅰ、Ⅱ，A、B 等；")
        
        # D1-6: 内容与实际相符
        self._add_result("请人工确认", "D1-6", "请确认工作内容与工作地点的描述是否合理一致。",
                         f"工作任务：“{task}”；工作地点：“{location}”。",
                         "确保票面填写的工作任务、工作地点、设备名称等没有出现上下不一致或明显的逻辑错误的情况。")

        # D1-7: 其他安措
        self._add_result("请人工确认", "D1-7", "请确认安全措施的物理执行情况和其他风险。",
                         f"票面硬件措施：“{hw}”。",
                         "请确认：1.是否填写“悬挂标志牌或装设遮栏”等操作，包括但不限于：在运行屏（柜）上进行工作时，应将检修设备与运行设备前后以明显的标志隔开；在工作地点或检修设备应设置“在此工作!”标志牌；作业点必要时可局部装设遮栏，并悬挂“在此工作！”标志牌；在开展涉及电源工作期间，在一经合闸即可送电到检修设备的断路器和隔离开关操作把手上，悬挂“禁止合闸，有人工作！”标志牌。；2.此栏目不填写无关内容，如“现场交底”、“电话通知”等,“安全交底”应填写在工作任务或者不填写；3. 是否存在其他措施不可控或安全隐患的情况。")
                
        # D1-14: 变更安措
        original_hw_measures = self.get_nested_value("工作要求的安全措施.硬件及工作环境,应设遮栏、应挂标示牌位置", "未填写")
        original_sw_measures = self.get_nested_value("工作要求的安全措施.软件及数据", "未填写")
        task_change = self.get_nested_value("工作变更.工作任务变更.增加的工作内容", "")
        owner_change_data = self.get_nested_value("工作变更.工作负责人变更", {})
        team_change = self.get_nested_value("工作变更.工作班成员变更", [])

        change_details = []
        if task_change:
            change_details.append(f"任务增加: {task_change}")
        if owner_change_data and any(owner_change_data.values()):
            orig_owner = owner_change_data.get('原工作负责人签名', '未知')
            new_owner = owner_change_data.get('现工作负责人签名', '未知')
            change_details.append(f"负责人变更: 从 {orig_owner} 变为 {new_owner}")
        if team_change:
            change_details.append(f"班组成员变更: {len(team_change)}条记录")

        if change_details:
            evidence = (f"【原始安措】硬件: “{original_hw_measures}”; 软件: “{original_sw_measures}”。\n"
                        f"【工作变更详情】{'; '.join(change_details)}。")
            self._add_result("请人工确认", "D1-14", "请判断已知的工作变更，是否需要对应修改安全措施。",
                             evidence,
                             "请基于以上原始安措和工作变更内容，判断变更是否实质上要求修改安措。若需要，则必须履行相应手续（如重新办理工作票或经许可人签名同意），否则违规。")

        # D1-15: 一人多票
        leader = self.get_nested_value("工作负责人（监护人）", "未知")
        self._add_result("请人工确认", "D1-15", "一个工作负责人是否同时执行两张或以上的工作票。",
                         f"本票工作负责人为：“{leader}”。此项检查需要关联其他工作票信息。", 
                         f"请查询负责人“{leader}”的所有在执行工作票，确保没有违规。")

        # # D1-16: 签名资格与真伪
        # all_signatures = self._collect_all_signatures()
        # sig_list_str = "; ".join([f"{role}: {name}" for role, name in all_signatures.items()]) if all_signatures else "无"
        # self._add_result("请人工确认", "D1-16", "请确认所有签名人资格及签名真伪。",
        #                  f"系统无法验证人员资格和笔迹。本票所有签名人汇总如下：{sig_list_str}",
        #                  "请确认所有签名人均具备相应资格，且为本人亲签，无冒签、代签。")

        # # D2-3: 盖章
        # self._add_result("请人工确认", "D2-3", "请确认是否已按规定盖“工作票终结”、“作废”章。",
        #                  "系统无法识别印章。", "请检查票面是否已加盖正确的印章。")

        # # D2-4: 附件
        # attachment = self.get_nested_value("工作要求的安全措施.附件", "")
        # if attachment and not any(k in attachment for k in ["无", "不涉及", "空"]):
        #     self._add_result("请人工确认", "D2-4", "请确认附件是否与工作票一同保存。",
        #                      f"票面“附件”栏注明：“{attachment}”。", "确保所有提及的附件都已正确归档。")
        # self._add_result("请人工确认", "D2-4", "请确认安全技术交底单、二次设备工作安全技术措施单是否与工作票一同保存。",
        #                      f"系统无法确认上述材料是否存在。", "确保安全技术交底单、二次设备工作安全技术措施单都已正确归档。")

        # # D2-5: 技术术语
        # self._add_result("请人工确认", "D2-5", "请确认票上使用的技术术语是否规范。",
        #                  "系统无法判断术语的行业规范性。", "请通读票面内容，检查是否存在不规范或易产生歧义的技术术语。")


if __name__ == '__main__':
    import json
    from ticket_parser import TicketParser

    def test_validation_logic():
        # 可在此处更改要测试的HTML文件
        html_file = rf'data/html/txgzp/txgzp2.html'
        print(f"--- 开始对文件进行自动化校对: {html_file} ---")
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
        except FileNotFoundError:
            print(f"错误: 测试文件 '{html_file}' 未找到。")
            return

        # 1. 解析HTML
        parser = TicketParser()
        ticket_data, parsing_errors = parser.parse(html_content)

        if parsing_errors:
            print("\n--- 解析时发生错误 ---")
            for error in parsing_errors:
                print(error)
        
        # 2. 执行规则检查
        validator = WorkTicketValidator(ticket_data)
        results = validator.validate()

        print("\n--- 校对结果 ---")
        print(json.dumps(results, indent=2, ensure_ascii=False))
        print("\n--- 校对结束 ---")

    test_validation_logic()
