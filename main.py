import logging
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse

from scripts.ticket_parser import TicketParser
from scripts.rule_check import WorkTicketValidator

from scripts.custom_rules_manager import CustomRuleManager
from scripts.config import Config
from typing import Dict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="工作票自动校对服务",
    description="接收HTML格式的工作票，解析并根据规则进行校对，返回不合格及不规范项。",
    version="1.0.0",
)

@app.post("/validate", summary="校对工作票")
async def validate_ticket(request: Request):
    """
    接收HTML内容，进行解析和校对。

    - **request body**: 需要包含原始的HTML字符串。
    - **return**: 返回一个JSON对象，包含`unqualified`, `nonstandard`, 和 `manual_checks` 三个列表。
    """
    try:
        html_content = await request.body()
        html_content = html_content.decode('utf-8')

        if not html_content:
            raise HTTPException(status_code=400, detail="未提供HTML内容")

        # 1. 解析工作票
        parser = TicketParser()
        ticket_data, parsing_errors = parser.parse(html_content)
        
        if parsing_errors:
            logger.warning(f"解析HTML时出现错误: {parsing_errors}")
            raise HTTPException(status_code=400, detail={"message": "HTML解析失败", "errors": parsing_errors})
            
        logger.info("工作票解析完成")

        # 2. 规则检查
        validator = WorkTicketValidator(ticket_data)
        results = validator.validate()
        logger.info(f"校对完成: {results}")

        return JSONResponse(content=results)

    except Exception as e:
        logger.error(f"处理请求时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {e}")

@app.post("/rules", summary="添加自定义校核规则")
async def add_rule(rule: Dict):
    """
    添加一条自定义校核规则。
    - **rule**: JSON对象，包含 rule_id, category, field, condition, description, evidence_template, suggestion, (可选)regex
    示例: {"rule_id": "custom-1", "category": "不合格", "field": "工作任务", "condition": "not_empty", "description": "工作任务不能为空", "evidence_template": "字段值: {value}", "suggestion": "请填写工作任务"}
    """
    config = Config()
    rules_file = config.get('custom_rules_file', 'custom_rules.json')
    manager = CustomRuleManager(rules_file)
    try:
        added_rule = manager.add_rule(rule)
        return {"message": "规则添加成功", "rule": added_rule}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/rules", summary="获取所有自定义规则")
async def get_rules():
    config = Config()
    rules_file = config.get('custom_rules_file', 'custom_rules.json')
    manager = CustomRuleManager(rules_file)
    return {"rules": manager.get_rules()}

@app.delete("/rules/{rule_id}", summary="删除自定义规则")
async def delete_rule(rule_id: str):
    config = Config()
    rules_file = config.get('custom_rules_file', 'custom_rules.json')
    manager = CustomRuleManager(rules_file)
    if manager.get_rule_by_id(rule_id):
        manager.delete_rule(rule_id)
        return {"message": f"规则 {rule_id} 删除成功"}
    raise HTTPException(status_code=404, detail="规则未找到")

@app.put("/rules/{rule_id}", summary="更新自定义规则")
async def update_rule(rule_id: str, updated_rule: Dict):
    config = Config()
    rules_file = config.get('custom_rules_file', 'custom_rules.json')
    manager = CustomRuleManager(rules_file)
    try:
        updated = manager.update_rule(rule_id, updated_rule)
        return {"message": "规则更新成功", "rule": updated}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.get("/", summary="服务健康检查")
def read_root():
    """
    服务健康检查端点。
    """
    return {"status": "ok", "message": "欢迎使用工作票自动校对服务"}

if __name__ == "__main__":
    # 使用 uvicorn 启动服务
    # 在生产环境中，建议使用 gunicorn + uvicorn workers
    # uvicorn.run(app, host="127.0.0.1", port=2345)
    uvicorn.run("main:app", host="127.0.0.1", port=2345, reload=True)
