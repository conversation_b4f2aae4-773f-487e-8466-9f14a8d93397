# 工作票校验规则配置指南

## 概述

本指南面向业务人员，介绍如何使用新的基于规则文件的工作票校验系统。通过配置JSON格式的规则文件，业务人员可以在不修改代码的情况下调整校验规则。

## 快速开始

### 1. 规则文件位置
规则文件位于：`config/validation_rules_simple.json`

### 2. 基本使用流程
1. 编辑规则文件
2. 保存文件
3. 运行校验程序
4. 查看校验结果

## 规则文件结构

规则文件是一个JSON数组，包含多个规则对象。每个规则对象有以下基本字段：

```json
{
  "rule_id": "D1-1-1",                    // 规则唯一标识
  "category": "不合格",                   // 问题类别
  "description": "工作票类型漏填",         // 问题描述
  "field_path": "工作票类型",             // 检查的字段路径
  "condition": "not_empty",               // 检查条件
  "evidence_template": "工作票类型字段为空", // 证据模板
  "suggestion": "请填写正确的工作票类型"    // 建议操作
}
```

## 常用校验条件

### 1. 空值检查 (not_empty)
检查字段是否为空。

**示例：检查工作票类型是否填写**
```json
{
  "rule_id": "D1-1-1",
  "category": "不合格",
  "description": "工作票类型漏填",
  "field_path": "工作票类型",
  "condition": "not_empty",
  "evidence_template": "工作票类型字段为空",
  "suggestion": "请填写正确的工作票类型"
}
```

### 2. 正则表达式检查 (matches_regex)
使用正则表达式检查字段格式。

**示例：检查时间格式**
```json
{
  "rule_id": "D2-6-1",
  "category": "不规范",
  "description": "计划开始时间格式不规范",
  "field_path": "计划工作时间.开始时间",
  "condition": "matches_regex",
  "regex": "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$",
  "evidence_template": "时间格式为{value}，不符合标准格式",
  "suggestion": "所有时间均应采用YYYY-MM-DD HH:MM格式"
}
```

### 3. 关键字检查 (contains_keywords)
检查字段是否包含指定关键字。

**示例：检查安全措施是否包含必要关键词**
```json
{
  "rule_id": "D1-7-1",
  "category": "不合格",
  "description": "硬件安全措施可能不完备",
  "field_path": "工作要求的安全措施.硬件及工作环境,应设遮栏、应挂标示牌位置",
  "condition": "contains_keywords",
  "keywords": ["遮栏", "标示牌", "标志牌", "警示带", "误碰", "隔离"],
  "match_mode": "any",
  "negate": true,
  "evidence_template": "硬件安措内容为：{value}，未找到必要的安全措施关键词",
  "suggestion": "请根据现场情况，补充必要的硬件安全措施"
}
```

### 4. 日期时间检查 (datetime_check)
检查日期时间的有效性。

**示例：检查计划时间是否过期**
```json
{
  "rule_id": "D1-2-3",
  "category": "不合格",
  "description": "计划工作时间已过期",
  "field_path": "计划工作时间.结束时间", 
  "condition": "datetime_check",
  "datetime_format": "%Y-%m-%d %H:%M",
  "check_type": "not_past",
  "evidence_template": "计划结束时间({value})早于当前时间",
  "suggestion": "请修改计划工作时间或作废此票"
}
```

### 5. 条件检查 (conditional_check)
基于其他字段的值来决定是否执行检查。

**示例：软件操作时检查备份措施**
```json
{
  "rule_id": "D1-7-4",
  "category": "不合格",
  "description": "软件操作未填写备份措施",
  "field_path": "工作要求的安全措施.软件及数据",
  "condition": "conditional_check",
  "if_condition": {
    "field_path": "工作任务",
    "condition": "contains_keywords",
    "keywords": ["软件", "数据库", "系统", "固件", "配置", "功能", "数据", "网络", "升级", "变更"],
    "match_mode": "any"
  },
  "then_check": {
    "condition": "contains_keywords",
    "keywords": ["备份"],
    "match_mode": "any",
    "negate": false
  },
  "evidence_template": "工作任务涉及软件操作，但软件安措{value}中未提及备份",
  "suggestion": "请在操作前增加数据和系统备份措施"
}
```

## 字段路径说明

字段路径用于指定要检查的数据字段，支持嵌套访问：

- 简单字段：`"工作票类型"`
- 嵌套字段：`"工作许可.许可时间"`
- 更深层嵌套：`"工作要求的安全措施.软件及数据"`

## 常见问题类别

- **不合格**：严重问题，工作票不能使用
- **不规范**：格式问题，需要修正但不影响使用
- **请人工确认**：需要人工判断的问题

## 运行校验

### 使用测试脚本
```bash
python scripts/test_rule_based_validator.py data/html/txgzp/txgzp2.html
```

### 查看结果
校验结果会显示：
- 规则执行统计
- 问题分类统计
- 详细的问题列表

## 修改规则的步骤

1. **备份原文件**：在修改前备份 `validation_rules_simple.json`
2. **编辑规则**：使用文本编辑器修改规则文件
3. **验证JSON格式**：确保JSON格式正确
4. **测试规则**：运行测试脚本验证规则效果
5. **部署使用**：确认无误后正式使用

## 注意事项

1. **JSON格式**：确保文件符合JSON格式，注意逗号和引号
2. **字段路径**：确保字段路径与实际数据结构匹配
3. **正则表达式**：正则表达式中的反斜杠需要双重转义
4. **规则ID**：每个规则的ID必须唯一
5. **测试验证**：修改后务必测试验证

## 技术支持

如需技术支持或有疑问，请联系开发团队。
